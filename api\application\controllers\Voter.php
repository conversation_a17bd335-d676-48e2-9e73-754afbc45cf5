<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Voter extends ApiController
{
	private $client_token;
    function __construct()
    {
        parent::__construct();
		$this->client_token = $this->token->validateToken();
    }
	
    function search_post()
    {
		if(!$this->client_token){
			return  $this->set_response($this->exitDanger('UnAuthorized User'), REST_Controller::HTTP_OK);
		}
		
        $this->load->model('voterModel');
		$this->form_validation->set_rules('userId', 'user id', 'trim|required|xss_clean');
        $this->form_validation->set_rules(
            'voterId',
            'Voter ID',
            'required|regex_match[/^[A-Z]{3}[0-9]{7}$/]',
            array(
                'required'    => 'The %s is required.',
                'regex_match' => 'The %s must be in format: 3 uppercase letters followed by 7 digits.'
            )
        );

        if ($this->form_validation->run() === false) {
            return $this->set_response($this->exitDanger(validation_errors()), REST_Controller::HTTP_OK);
        } else {

            $voterId =(string) $this->input->post("voterId");
			$user_id = $this->input->post("userId");

			$date = new DateTime("now");
			$check_date = $date->format('Y-m-d');
			$curr_date = $date->format('Y-m-d h:i:s');

			$this->load->model('voterModel');
			$this->load->model('userModel');
            $this->load->model('VerificationTokenModel');

			if ($user_id) {
				$returnData = $this->userModel->findOne(['id' => $user_id]);
				if (!isObject($returnData)) {
					unset($mobile);
					unset($returnData);
					return $this->set_response($this->exitDanger('Invalid User'), REST_Controller::HTTP_OK);
				}
			}
							
			$users = $this->token->userlimit($user_id, 'voter');

			if(!$users || empty($users)){
				return $this->set_response($this->exitDanger("User not exist!!"), REST_Controller::HTTP_OK);die;
			}else{
				if($users['status']){
					$userlimit = $users['error'];
				}else{
					return $this->set_response($this->exitDanger($users['error']), REST_Controller::HTTP_OK);die;
				}
			}
			
			$truecallerDataCount = $this->voterModel->count(['userId' => $user_id,'DATE(created_at)'=>$check_date]);
        
            if($truecallerDataCount >=$userlimit){
                return $this->set_response($this->exitDanger('Daily limit over.Please try tomorrow.'), REST_Controller::HTTP_OK);die;
            }

            // Get active token for license service
            $tokenRow = $this->VerificationTokenModel->getActiveToken('voter');
            if (!$tokenRow) {
                return $this->set_response($this->exitDanger("No active API token available."), REST_Controller::HTTP_OK);
            }
            $apiToken = $tokenRow->token;
            
            $voterPayload = json_encode([
                "method"      => "votervalidate",
                "txn_id"      => "sfsgdsg",
                "clientid"    => "222",
                "voternumber" => $voterId
            ]);

            writeLog($voterPayload, 'voter-api-payload');

            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://voter-card-verification.p.rapidapi.com/Getvoterfulldetails',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => $voterPayload,
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json',
                    'x-rapidapi-host: voter-card-verification.p.rapidapi.com',
                    'x-rapidapi-key: '.$apiToken
                ),
            ));
            $voterApiResponse = curl_exec($curl);
            curl_close($curl);

            writeLog($voterApiResponse, 'voter-api-response');

            $voterApiData = json_decode($voterApiResponse, true);

            $status = 'FAIL';
            $dataInsert = '';

            if (isset($voterApiData['Succeeded']['voter_Details'])) {
                $dataInsert = json_encode($voterApiData['Succeeded']['voter_Details']);
                $status = isset($voterApiData['Succeeded']['statusCode']) && $voterApiData['Succeeded']['statusCode'] == 200 ? 1 : 0;
            }

            $this->VerificationTokenModel->incrementTokenUsage($tokenRow->id);

            $inserData = [
                'userId' => $user_id,
                'response' => $dataInsert,
                'status' => $status,
                'voter_number' => $voterId,
                'created_at' => $curr_date
            ];

            $this->voterModel->attach($inserData);

            if (isset($voterApiData['Succeeded']['voter_Details'])) {
                $d = $voterApiData['Succeeded']['voter_Details'];
                $message  = "<p></p><h3>Voter Card Verification Details</h3><p>";
                $message .= "<b>Name:</b> " . (isset($d['fullName']) ? $d['fullName'] : 'N/A') . "<br>";
                $message .= "<b>EPIC Number:</b> " . (isset($d['epicNumber']) ? $d['epicNumber'] : 'N/A') . "<br>";
                $message .= "<b>Father/Relation Name:</b> " . (isset($d['relationName']) ? $d['relationName'] : 'N/A') . "<br>";
                $message .= "<b>Age:</b> " . (isset($d['age']) ? $d['age'] : 'N/A') . "<br>";
                $message .= "<b>Gender:</b> " . (isset($d['gender']) ? $d['gender'] : 'N/A') . "<br>";
                $message .= "<b>District:</b> " . (isset($d['districtValue']) ? $d['districtValue'] : 'N/A') . "<br>";
                $message .= "<b>State:</b> " . (isset($d['stateName']) ? $d['stateName'] : 'N/A') . "<br>";
                $message .= "<b>Assembly Name:</b> " . (isset($d['asmblyName']) ? $d['asmblyName'] : 'N/A') . "<br>";
                $message .= "<b>Part Name:</b> " . (isset($d['partName']) ? $d['partName'] : 'N/A') . "<br>";
                $message .= "<b>Building Address:</b> " . (isset($d['buildingAddress']) ? $d['buildingAddress'] : 'N/A') . "<br>";
                $message .= "<b>Applicant First Name:</b> " . (isset($d['applicantFirstName']) ? $d['applicantFirstName'] : 'N/A') . "<br>";
                $message .= "<b>Relation Name L1:</b> " . (isset($d['relationNameL1']) ? $d['relationNameL1'] : 'N/A') . "<br>";
                $message .= "<b>Part Number:</b> " . (isset($d['partNumber']) ? $d['partNumber'] : 'N/A') . "<br>";
                $message .= "<b>Part ID:</b> " . (isset($d['partId']) ? $d['partId'] : 'N/A') . "<br>";
                $message .= "<b>Part Serial Number:</b> " . (isset($d['partSerialNumber']) ? $d['partSerialNumber'] : 'N/A') . "<br>";
                $message .= "<b>AC ID:</b> " . (isset($d['acId']) ? $d['acId'] : 'N/A') . "<br>";
                $message .= "<b>AC Number:</b> " . (isset($d['acNumber']) ? $d['acNumber'] : 'N/A') . "<br>";
                $message .= "<b>Parliament Name:</b> " . (isset($d['prlmntName']) ? $d['prlmntName'] : 'N/A') . "<br>";
                $message .= "<b>Parliament No:</b> " . (isset($d['prlmntNo']) ? $d['prlmntNo'] : 'N/A') . "<br>";
                $message .= "<b>District Code:</b> " . (isset($d['districtCd']) ? $d['districtCd'] : 'N/A') . "<br>";
                $message .= "<b>District No:</b> " . (isset($d['districtNo']) ? $d['districtNo'] : 'N/A') . "<br>";
                $message .= "<b>State Name L1:</b> " . (isset($d['stateNameL1']) ? $d['stateNameL1'] : 'N/A') . "<br>";
                $message .= "<b>State ID:</b> " . (isset($d['stateId']) ? $d['stateId'] : 'N/A') . "<br>";
                $message .= "<b>State Code:</b> " . (isset($d['stateCd']) ? $d['stateCd'] : 'N/A') . "<br>";
                $message .= "<b>Is Active:</b> " . (isset($d['isActive']) ? ($d['isActive'] ? 'Yes' : 'No') : 'N/A') . "<br>";
                $message .= "<b>Relation Type:</b> " . (isset($d['relationType']) ? $d['relationType'] : 'N/A') . "<br>";
                $message .= "<b>Created DateTime:</b> " . (isset($d['createdDttm']) ? $d['createdDttm'] : 'N/A') . "<br>";
                $message .= "<b>Modified DateTime:</b> " . (isset($d['modifiedDttm']) ? $d['modifiedDttm'] : 'N/A') . "<br>";
                $message .= "<b>Section No:</b> " . (isset($d['sectionNo']) ? $d['sectionNo'] : 'N/A') . "<br>";
                $message .= "<b>PS Building Name:</b> " . (isset($d['psbuildingName']) ? $d['psbuildingName'] : 'N/A') . "<br>";
                $message .= "<b>EPIC Datetime:</b> " . (isset($d['epicDatetime']) ? $d['epicDatetime'] : 'N/A') . "<br>";
                $message .= "<b>Full Name L1:</b> " . (isset($d['fullNameL1']) ? $d['fullNameL1'] : 'N/A') . "<br>";
                $message .= "<b>Relative Full Name:</b> " . (isset($d['relativeFullName']) ? $d['relativeFullName'] : 'N/A') . "<br>";
                $message .= "<b>Relative Full Name L1:</b> " . (isset($d['relativeFullNameL1']) ? $d['relativeFullNameL1'] : 'N/A') . "<br>";
                $message .= "<b>PS Room Details:</b> " . (isset($d['psRoomDetails']) ? $d['psRoomDetails'] : 'N/A') . "<br>";
                $message .= "<b>PS Room Details L1:</b> " . (isset($d['psRoomDetailsL1']) ? $d['psRoomDetailsL1'] : 'N/A') . "<br>";
                $message .= "<b>Building Address L1:</b> " . (isset($d['buildingAddressL1']) ? $d['buildingAddressL1'] : 'N/A') . "<br>";
                $message .= "<b>PS Building Name L1:</b> " . (isset($d['psBuildingNameL1']) ? $d['psBuildingNameL1'] : 'N/A') . "<br>";
                $message .= "</p>";

                $data = [ "pi" => $message ];
                return $this->set_response($this->exitSuccessWithMultiple($data), REST_Controller::HTTP_OK);
            } else {
                return $this->set_response($this->exitDanger("No Data Found"), REST_Controller::HTTP_OK);
            }
            
        }
    }
	
	public function getDetails($patterns,$response,$result){
		foreach ($patterns as $key => $pattern) {
			if (preg_match($pattern, $response, $matches)) {
				$result .= "<b>".$key."</b>: ".$matches[1]."<br>";
			} else {
				$result .= "<b>".$key."</b>: ".'N/A'."<br>";
			}
		}
		return $result;
	}
	
	function history_post()
    {
		if(!$this->client_token){
			return  $this->set_response($this->exitDanger('UnAuthorized User'), REST_Controller::HTTP_OK);
		}
		$this->form_validation->set_rules('userId', 'user id', 'trim|required|xss_clean');
		$this->form_validation->set_rules('searchItem', 'searchItem', 'trim|xss_clean');
		$this->form_validation->set_rules('to', 'to', 'trim|xss_clean');
		$this->form_validation->set_rules('from', 'from', 'trim|xss_clean');
		$this->form_validation->set_rules('offset', 'offset', 'trim|xss_clean');

        if ($this->form_validation->run() === false) {
            return $this->set_response($this->exitDanger(validation_errors()), REST_Controller::HTTP_OK);
        }
		
        /* date range */
        $from = $this->input->get('from');
        $to = $this->input->get('to');
        if ($from != '' && $to != '') {
            $from = dbDate($from);
            $to = dbDate($to);
        } elseif ($from == '' && $to != '') {
            $from = dbDate($to);
            $to = dbDate($to);
        } elseif ($from != '' && $to == '') {
            $from = dbDate($from);
            $to = dbDate($from);
        }
        /* /date range/ */

        $searchItem = $this->input->post('searchItem');
        $user_id =  $this->input->post('userId');
        $where = 't1.userId='.$user_id;
        $orderbyField = 't1.created_at';
        $orderbyMethod = 'Desc';
        if ($searchItem != '') {
            $where .= ' AND (t1.voter_number like "%' . $searchItem . '%")';
        }
        if ($from != '' && $to != '') {
            if ($where != ''):
                $where .= ' AND ( DATE(t1.created_at) >=  "' . $from . '" && DATE(t1.created_at) <=  "' . $to . '")';
            else:
                $where = ' ( DATE(t1.created_at) >=  "' . $from . '" && DATE(t1.created_at) <=  "' . $to . '")';
            endif;
        }
        
        
        $perPage = 50;$offset=0;
		$offset=$this->input->post('offset');
        $this->load->model('voterModel');
        $totalRows = $this->voterModel->totalCount($where);
        $offset = ($offset > $totalRows) ? 0 : $offset;
        $data = $this->voterModel->getAll($where,null, ['orderBy' => [$orderbyField, $orderbyMethod],
            "limit" => ['perPage' => $perPage, 'offset' => $offset]]);

        // Get daily search count for today's date
        $today = date('Y-m-d');
        $dailySearchCount = $this->voterModel->count(['userId' => $user_id, 'DATE(created_at)' => $today]);

        // Get daily credit using helper function
        $dailyCredit = $this->token->getDailyCredit($user_id, 'voter');

        $config['total_rows'] = $totalRows;
		$config['per_page'] = $perPage;
 		$config['data'] = $data;
 		$config['daily_search'] = $dailySearchCount;
 		$config['daily_credit'] = $dailyCredit;
       	return $this->set_response($this->exitSuccessWithMultiple($config), REST_Controller::HTTP_OK);
				
    }
	function detail_get($id)
    {
		if(!$this->client_token){
			return  $this->set_response($this->exitDanger('UnAuthorized User'), REST_Controller::HTTP_OK);
		}
        if ($id) {
            $this->load->model('voterModel');
            $value = $this->voterModel->findOne(['id' => $id], '*');
            if (isObject($value)) {
                if(isset($value->response) && $value->status){
                    $d = json_decode($value->response, true);
                    if (is_array($d)) {
                        $message  = "<p></p><h3>Voter Card Verification Details</h3><p>";
                        $message .= "<b>Name:</b> " . (isset($d['fullName']) ? $d['fullName'] : 'N/A') . "<br>";
                        $message .= "<b>EPIC Number:</b> " . (isset($d['epicNumber']) ? $d['epicNumber'] : 'N/A') . "<br>";
                        $message .= "<b>Father/Relation Name:</b> " . (isset($d['relationName']) ? $d['relationName'] : 'N/A') . "<br>";
                        $message .= "<b>Age:</b> " . (isset($d['age']) ? $d['age'] : 'N/A') . "<br>";
                        $message .= "<b>Gender:</b> " . (isset($d['gender']) ? $d['gender'] : 'N/A') . "<br>";
                        $message .= "<b>District:</b> " . (isset($d['districtValue']) ? $d['districtValue'] : 'N/A') . "<br>";
                        $message .= "<b>State:</b> " . (isset($d['stateName']) ? $d['stateName'] : 'N/A') . "<br>";
                        $message .= "<b>Assembly Name:</b> " . (isset($d['asmblyName']) ? $d['asmblyName'] : 'N/A') . "<br>";
                        $message .= "<b>Part Name:</b> " . (isset($d['partName']) ? $d['partName'] : 'N/A') . "<br>";
                        $message .= "<b>Building Address:</b> " . (isset($d['buildingAddress']) ? $d['buildingAddress'] : 'N/A') . "<br>";
                        $message .= "<b>Applicant First Name:</b> " . (isset($d['applicantFirstName']) ? $d['applicantFirstName'] : 'N/A') . "<br>";
                        $message .= "<b>Relation Name L1:</b> " . (isset($d['relationNameL1']) ? $d['relationNameL1'] : 'N/A') . "<br>";
                        $message .= "<b>Part Number:</b> " . (isset($d['partNumber']) ? $d['partNumber'] : 'N/A') . "<br>";
                        $message .= "<b>Part ID:</b> " . (isset($d['partId']) ? $d['partId'] : 'N/A') . "<br>";
                        $message .= "<b>Part Serial Number:</b> " . (isset($d['partSerialNumber']) ? $d['partSerialNumber'] : 'N/A') . "<br>";
                        $message .= "<b>AC ID:</b> " . (isset($d['acId']) ? $d['acId'] : 'N/A') . "<br>";
                        $message .= "<b>AC Number:</b> " . (isset($d['acNumber']) ? $d['acNumber'] : 'N/A') . "<br>";
                        $message .= "<b>Parliament Name:</b> " . (isset($d['prlmntName']) ? $d['prlmntName'] : 'N/A') . "<br>";
                        $message .= "<b>Parliament No:</b> " . (isset($d['prlmntNo']) ? $d['prlmntNo'] : 'N/A') . "<br>";
                        $message .= "<b>District Code:</b> " . (isset($d['districtCd']) ? $d['districtCd'] : 'N/A') . "<br>";
                        $message .= "<b>District No:</b> " . (isset($d['districtNo']) ? $d['districtNo'] : 'N/A') . "<br>";
                        $message .= "<b>State Name L1:</b> " . (isset($d['stateNameL1']) ? $d['stateNameL1'] : 'N/A') . "<br>";
                        $message .= "<b>State ID:</b> " . (isset($d['stateId']) ? $d['stateId'] : 'N/A') . "<br>";
                        $message .= "<b>State Code:</b> " . (isset($d['stateCd']) ? $d['stateCd'] : 'N/A') . "<br>";
                        $message .= "<b>Is Active:</b> " . (isset($d['isActive']) ? ($d['isActive'] ? 'Yes' : 'No') : 'N/A') . "<br>";
                        $message .= "<b>Relation Type:</b> " . (isset($d['relationType']) ? $d['relationType'] : 'N/A') . "<br>";
                        $message .= "<b>Created DateTime:</b> " . (isset($d['createdDttm']) ? $d['createdDttm'] : 'N/A') . "<br>";
                        $message .= "<b>Modified DateTime:</b> " . (isset($d['modifiedDttm']) ? $d['modifiedDttm'] : 'N/A') . "<br>";
                        $message .= "<b>Section No:</b> " . (isset($d['sectionNo']) ? $d['sectionNo'] : 'N/A') . "<br>";
                        $message .= "<b>PS Building Name:</b> " . (isset($d['psbuildingName']) ? $d['psbuildingName'] : 'N/A') . "<br>";
                        $message .= "<b>EPIC Datetime:</b> " . (isset($d['epicDatetime']) ? $d['epicDatetime'] : 'N/A') . "<br>";
                        $message .= "<b>Full Name L1:</b> " . (isset($d['fullNameL1']) ? $d['fullNameL1'] : 'N/A') . "<br>";
                        $message .= "<b>Relative Full Name:</b> " . (isset($d['relativeFullName']) ? $d['relativeFullName'] : 'N/A') . "<br>";
                        $message .= "<b>Relative Full Name L1:</b> " . (isset($d['relativeFullNameL1']) ? $d['relativeFullNameL1'] : 'N/A') . "<br>";
                        $message .= "<b>PS Room Details:</b> " . (isset($d['psRoomDetails']) ? $d['psRoomDetails'] : 'N/A') . "<br>";
                        $message .= "<b>PS Room Details L1:</b> " . (isset($d['psRoomDetailsL1']) ? $d['psRoomDetailsL1'] : 'N/A') . "<br>";
                        $message .= "<b>Building Address L1:</b> " . (isset($d['buildingAddressL1']) ? $d['buildingAddressL1'] : 'N/A') . "<br>";
                        $message .= "<b>PS Building Name L1:</b> " . (isset($d['psBuildingNameL1']) ? $d['psBuildingNameL1'] : 'N/A') . "<br>";
                        $message .= "</p>";
                        $data = [ "pi" => $message ];
                        return $this->set_response($this->exitSuccessWithMultiple($data), REST_Controller::HTTP_OK);
                    } else {
                        return $this->set_response($this->exitDanger("No Data Found"), REST_Controller::HTTP_OK);
                	}
                }else{
                    return $this->set_response($this->exitDanger("No Data Found"), REST_Controller::HTTP_OK);	
                }
            } else {
               return $this->set_response($this->exitDanger("No Data Found"), REST_Controller::HTTP_OK);	
            }
        } else {
           	return $this->set_response($this->exitDanger("Please check request"), REST_Controller::HTTP_OK);	
        }
    }

}
