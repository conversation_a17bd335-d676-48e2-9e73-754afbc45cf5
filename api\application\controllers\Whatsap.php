<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Whatsap extends ApiController
{
	private $client_token;
    function __construct()
    {
        parent::__construct();
		$this->client_token = $this->token->validateToken();
    }
	
    function search_post()
    {
		if(!$this->client_token){
			//return  $this->set_response($this->exitDanger('UnAuthorized User'), REST_Controller::HTTP_OK);
		}
		
        $this->load->model('whatsapModel');
		$this->form_validation->set_rules('userId', 'user id', 'trim|required|xss_clean');
		$this->form_validation->set_rules('mobile', 'mobile', 'trim|required|xss_clean');
        if ($this->form_validation->run() === false) {
            return $this->set_response($this->exitDanger(validation_errors()), REST_Controller::HTTP_OK);
        } else {
            $aadhaar =(string) $this->input->post("mobile");
			$user_id = $this->input->post("userId");
			$date = new DateTime("now");
			$check_date = $date->format('Y-m-d');
			$curr_date = $date->format('Y-m-d h:i:s');
			$this->load->model('whatsapModel');
			$this->load->model('userModel');
			if ($user_id) {
				$returnData = $this->userModel->findOne(['id' => $user_id]);
				if (!isObject($returnData)) {
					unset($mobile);
					unset($returnData);
					return $this->set_response($this->exitDanger('Invalid User'), REST_Controller::HTTP_OK);
				}
			}
							
			$users = $this->token->userlimit($user_id, 'whatsapp');
			if(!$users || empty($users)){
				return $this->set_response($this->exitDanger("User not exist!!"), REST_Controller::HTTP_OK);die;
			}else{
				if($users['status']){
					$userlimit = $users['error'];
				}else{
					return $this->set_response($this->exitDanger($users['error']), REST_Controller::HTTP_OK);die;
				}
			}
			
			// Get daily search count from external API
			$dailySearchPost = [
				"from" => $check_date,
				"to" => $check_date,
				"id" => $user_id,
				"offset" => 0,
				"searchItem" => ""
			];
			$dailySearchParam = json_encode($dailySearchPost);

			$dailySearchCurl = curl_init();
			curl_setopt_array($dailySearchCurl, array(
				CURLOPT_URL => "https://eitem.in/vehicle/history-whatsap.php",
				CURLOPT_RETURNTRANSFER => true,
				CURLOPT_ENCODING => '',
				CURLOPT_MAXREDIRS => 10,
				CURLOPT_TIMEOUT => 0,
				CURLOPT_FOLLOWLOCATION => true,
				CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				CURLOPT_CUSTOMREQUEST => 'POST',
				CURLOPT_SSL_VERIFYPEER => false,
				CURLOPT_SSL_VERIFYHOST => false,
				CURLOPT_POSTFIELDS => $dailySearchParam,
				CURLOPT_HTTPHEADER => array(
					'Content-Type: application/json',
					'Authorization: ma7641e1064c36f27fa1fd802ea436av5b0a553ead9f97f39de75f2f51b25a983236nish'
				),
			));
			$dailySearchResponse = curl_exec($dailySearchCurl);
			curl_close($dailySearchCurl);

			$dailySearchData = json_decode($dailySearchResponse);
			$truecallerDataCount = (isset($dailySearchData->status) && $dailySearchData->status != '') ? $dailySearchData->totalRows : 0;

			if($truecallerDataCount >=$userlimit){
				return $this->set_response($this->exitDanger('Daily limit over.Please try tomorrow.'), REST_Controller::HTTP_OK);die;
			}

			$post = [
				"mobile"=> $aadhaar,
				"id"=>$user_id
			];
			
			$param = json_encode($post);
			$curl = curl_init();
			$url = "https://eitem.in/vehicle/fetch-whatsap.php";					

			curl_setopt_array($curl, array(
				CURLOPT_URL => $url,
				CURLOPT_RETURNTRANSFER => false,
				CURLOPT_ENCODING => '',
				CURLOPT_MAXREDIRS => 10,
				CURLOPT_TIMEOUT => 2,
				CURLOPT_FOLLOWLOCATION => true,
				CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				CURLOPT_CUSTOMREQUEST => 'POST',
				CURLOPT_SSL_VERIFYPEER => false,
				CURLOPT_SSL_VERIFYHOST => false,
				CURLOPT_POSTFIELDS =>$param,
				CURLOPT_HTTPHEADER => array(
					'Content-Type: application/json',
					'Authorization: ma7641e1064c36f27fa1fd802ea436av5b0a553ead9f97f39de75f2f51b25a983236nish'
				),
			));

			$response = curl_exec($curl);
			//$responseCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
			//$responses = json_decode($response);
			curl_close($curl);
			$mobiles = explode(" " ,$aadhaar);
			$timeout = 1 * count($mobiles);
			return $this->set_response($this->exitSuccessWithMultiple("Your request received. You can check data on history after ".$timeout. " minutes"), REST_Controller::HTTP_OK);
			
			/*if(isset($responses->response)){
				$dataInsert = $responses->response;
			}else{
				$dataInsert = '';
			}
			if(isset($responses->status)){
				$status = $responses->status;
				$url = $responses->url;
				$name = $responses->name;
				$details = $responses->details;
				$statusDetails = $responses->statusDetails;
			}else{
				$status = 'FAIL';
				$statusDetails = isset($responses->statusData) ? $responses->statusData : 'N/A';
				$details = isset($responses->details) ? $responses->details : 'N/A';
				$name = isset($responses->name) ? $responses->name : 'N/A';
				$url = isset($responses->url) ? $responses->url : 'N/A';
				$aadhaar = isset($responses->mobile) ? $responses->mobile : 'N/A';
			}
			$inserData = [];
			$inserData = [
				'userId' => $user_id,
				'response' => $dataInsert,
				'status' => $status,
				'mobile' => $aadhaar,
				'url' => $url,
				'name' => $name,
				'details' => $details,
				'statusDetails' => $statusDetails,
				'created_at' => $curr_date
			];

			$this->whatsapModel->attach($inserData);
			if(isset($responses->name) && $responses->status){
				// Array to hold extracted data
				$statusDetails = isset($statusDetails) ? $statusDetails : 'N/A';
				$details = isset($details) ? $details : 'N/A';
				$name = isset($name) ? $name : 'N/A';
				$url = isset($url) ? $url : 'N/A';
				

						// Define regex patterns for each field
						$patterns = [
							'Name' => $name,
							'Details' => $details,
							'Status' => $statusDetails,
							'Mobile' => $aadhaar,
							'Profile Image' => $url
						];
				$result = "<p></p><h3>Mobile Information</h3><p>";						
				$result = $this->getDetails($patterns,$responses->name,$result);
				$result .= "</p>";


				$data = [ "pi" =>$result];
				return $this->set_response($this->exitSuccessWithMultiple($data), REST_Controller::HTTP_OK);
			}else{
				if(isset($responses->response)){
					return $this->set_response($this->exitDanger($responses->response), REST_Controller::HTTP_OK);
				}else{
					return $this->set_response($this->exitDanger("Some issue occured. Please contact admin"), REST_Controller::HTTP_OK);
				}	
			}*/			
        }
    }
	
	public function getDetails($patterns,$response,$result){
		foreach ($patterns as $key => $pattern) {
			if($key == 'Profile Image'){
				$pattern = "<img src='".$pattern."' />";
			}
			$result .= "<b>".$key."</b>: ".$pattern."<br>";
		}
		return $result;
	}
	
	function history_post()
    {
		if(!$this->client_token){
			//return  $this->set_response($this->exitDanger('UnAuthorized User'), REST_Controller::HTTP_OK);
		}
		$this->form_validation->set_rules('userId', 'user id', 'trim|required|xss_clean');
		$this->form_validation->set_rules('searchItem', 'searchItem', 'trim|xss_clean');
		$this->form_validation->set_rules('to', 'to', 'trim|xss_clean');
		$this->form_validation->set_rules('from', 'from', 'trim|xss_clean');
		$this->form_validation->set_rules('offset', 'offset', 'trim|xss_clean');

        if ($this->form_validation->run() === false) {
            return $this->set_response($this->exitDanger(validation_errors()), REST_Controller::HTTP_OK);
        }
		
        /* date range */
        $from 	= $this->input->get('from');
        $to 	= $this->input->get('to');
		$offset = $this->input->get('offset');
        if ($from != '' && $to != '') {
            $from = dbDate($from);
            $to = dbDate($to);
        } elseif ($from == '' && $to != '') {
            $from = dbDate($to);
            $to = dbDate($to);
        } elseif ($from != '' && $to == '') {
            $from = dbDate($from);
            $to = dbDate($from);
        }
        /* /date range/ */

        $searchItem = $this->input->post('searchItem');
        $user_id =  $this->input->post('userId');
        $where = 't1.userId='.$user_id;
        $orderbyField = 't1.created_at';
        $orderbyMethod = 'Desc';
        /*if ($searchItem != '') {
            $where .= ' AND (t1.mobile like "%' . $searchItem . '%")';
        }
        if ($from != '' && $to != '') {
            if ($where != ''):
                $where .= ' AND ( DATE(t1.created_at) >=  "' . $from . '" && DATE(t1.created_at) <=  "' . $to . '")';
            else:
                $where = ' ( DATE(t1.created_at) >=  "' . $from . '" && DATE(t1.created_at) <=  "' . $to . '")';
            endif;
        }*/
		$post = [
			"from"=> $from,
			"to"=> $to,
			"id"=>$user_id,
			"offset"=>$offset,
			"searchItem"=>$searchItem
		];
        $param = json_encode($post);
        
        $perPage = 50;$offset=0;
		$curl = curl_init();
		$url = "https://eitem.in/vehicle/history-whatsap.php";					

		curl_setopt_array($curl, array(
			CURLOPT_URL => $url,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_SSL_VERIFYPEER => false,
			CURLOPT_SSL_VERIFYHOST => false,
			CURLOPT_POSTFIELDS =>$param,
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json',
				'Authorization: ma7641e1064c36f27fa1fd802ea436av5b0a553ead9f97f39de75f2f51b25a983236nish'
			),
		));
		$response = curl_exec($curl);
		$responseCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

		curl_close($curl);
		
		$responses = json_decode($response);

		if(isset($responses->status) && $responses->status!=''){
			$config['total_rows'] 	= $totalRows = $responses->totalRows;
			$config['per_page'] 	= $perPage = $responses->perPage;
			$this->viewDataBag->users = $responses->data;
		}else{
			$config['total_rows'] 	= $totalRows = 0;
			$config['per_page'] 	= $perPage = 0;
			$this->viewDataBag->users = [];
		}
		$config['data'] = $this->viewDataBag->users;

		// Get daily search count for today's date from external API
		$today = date('Y-m-d');
		$dailySearchPost = [
			"from" => $today,
			"to" => $today,
			"id" => $user_id,
			"offset" => 0,
			"searchItem" => ""
		];
		$dailySearchParam = json_encode($dailySearchPost);

		$dailySearchCurl = curl_init();
		curl_setopt_array($dailySearchCurl, array(
			CURLOPT_URL => "https://eitem.in/vehicle/history-whatsap.php",
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_SSL_VERIFYPEER => false,
			CURLOPT_SSL_VERIFYHOST => false,
			CURLOPT_POSTFIELDS => $dailySearchParam,
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json',
				'Authorization: ma7641e1064c36f27fa1fd802ea436av5b0a553ead9f97f39de75f2f51b25a983236nish'
			),
		));
		$dailySearchResponse = curl_exec($dailySearchCurl);
		curl_close($dailySearchCurl);

		$dailySearchData = json_decode($dailySearchResponse);
		$dailySearchCount = (isset($dailySearchData->status) && $dailySearchData->status != '') ? $dailySearchData->totalRows : 0;

		// Get daily credit using helper function
		$dailyCredit = getDailyCredit($user_id, 'whatsapp');

		$config['daily_search'] = $dailySearchCount;
		$config['daily_credit'] = $dailyCredit;
        $this->load->model('whatsapModel');
        /*$totalRows = $this->whatsapModel->totalCount($where);
        $offset = ($offset > $totalRows) ? 0 : $offset;
        $data = $this->whatsapModel->getAll($where,null, ['orderBy' => [$orderbyField, $orderbyMethod],
            "limit" => ['perPage' => $perPage, 'offset' => $offset]]);
        $config['total_rows'] = $totalRows;
		$config['per_page'] = $perPage;
 		$config['data'] = $data;*/
       	return $this->set_response($this->exitSuccessWithMultiple($config), REST_Controller::HTTP_OK);
				
    }
	function detail_get($id)
    {
		if(!$this->client_token){
			//return  $this->set_response($this->exitDanger('UnAuthorized User'), REST_Controller::HTTP_OK);
		}
        if ($id) {
            $this->load->model('whatsapModel');
			$post = [				
				"id"=>$id
			];
			$param = json_encode($post);
			$curl = curl_init();
			$url = "https://eitem.in/vehicle/detail-whatsap.php";					

			curl_setopt_array($curl, array(
				CURLOPT_URL => $url,
				CURLOPT_RETURNTRANSFER => true,
				CURLOPT_ENCODING => '',
				CURLOPT_MAXREDIRS => 10,
				CURLOPT_TIMEOUT => 0,
				CURLOPT_FOLLOWLOCATION => true,
				CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				CURLOPT_CUSTOMREQUEST => 'POST',
				CURLOPT_SSL_VERIFYPEER => false,
				CURLOPT_SSL_VERIFYHOST => false,
				CURLOPT_POSTFIELDS =>$param,
				CURLOPT_HTTPHEADER => array(
					'Content-Type: application/json',
					'Authorization: ma7641e1064c36f27fa1fd802ea436av5b0a553ead9f97f39de75f2f51b25a983236nish'
				),
			));
			$response = curl_exec($curl);
			log_message('error', 'whatsapDetails$response'.$response);
			$responseCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

			//$responses = json_decode($response);
			curl_close($curl);

			$responses = json_decode($response);
            if(isset($responses->status) && $responses->status!=''){
				$result = $result_vi = $result_id = '';					
								
				$data = [];
				foreach(json_decode($responses->data) as $value){
					// Array to hold extracted data
					$statusDetails = isset($value->statusData) ? $value->statusData : 'N/A';
					$details = isset($value->details) ? $value->details : 'N/A';
					$name = isset($value->name) ? $value->name : 'N/A';
					$url = isset($value->url) ? $value->url : 'N/A';
					$aadhaar = isset($value->mobile) ? $value->mobile : 'N/A';
					$result = "<p></p><h3>Mobile - $aadhaar</h3><p>";		


					// Define regex patterns for each field
					$patterns = [
						'Name' => $name,
						'Details' => $details,
						'Status' => $statusDetails,
						//'Mobile' => $aadhaar,
						'Profile Image' => $url
					];
						
						
					$result = $this->getDetails($patterns,$value->details,$result);
					$result .= "</p>";
					$data[] = $result;
				}				
				$datas = [ "pi" =>$data];
				return $this->set_response($this->exitSuccessWithMultiple($datas), REST_Controller::HTTP_OK);
            } else {
                return $this->set_response($this->exitDanger("No Data Found"), REST_Controller::HTTP_OK);	
            }
        } else {
            	return $this->set_response($this->exitDanger("Please check request"), REST_Controller::HTTP_OK);	
        }
				
    }
	
	/* api for mohit*/
	
	 function find_post()
    {
		if(!$this->client_token){
			//return  $this->set_response($this->exitDanger('UnAuthorized User'), REST_Controller::HTTP_OK);
		}
		
        $this->load->model('whatsapModel');
		$this->form_validation->set_rules('userId', 'user id', 'trim|required|xss_clean');
		$this->form_validation->set_rules('mobile', 'mobile', 'trim|required|xss_clean');
        if ($this->form_validation->run() === false) {
            return $this->set_response($this->exitDanger(validation_errors()), REST_Controller::HTTP_OK);
        } else {
            $aadhaar =(string) $this->input->post("mobile");
			$user_id = $this->input->post("userId");
			$date = new DateTime("now");
			$check_date = $date->format('Y-m-d');
			$curr_date = $date->format('Y-m-d h:i:s');
			$this->load->model('whatsapModel');
			$this->load->model('userModel');
			if ($user_id) {
				$returnData = $this->userModel->findOne(['id' => $user_id]);
				if (!isObject($returnData)) {
					unset($mobile);
					unset($returnData);
					return $this->set_response($this->exitDanger('Invalid User'), REST_Controller::HTTP_OK);
				}
			}
							
			$users = $this->token->userlimit($user_id, 'whatsapp');
			if(!$users || empty($users)){
				return $this->set_response($this->exitDanger("User not exist!!"), REST_Controller::HTTP_OK);die;
			}else{
				if($users['status']){
					$userlimit = $users['error'];
				}else{
					return $this->set_response($this->exitDanger($users['error']), REST_Controller::HTTP_OK);die;
				}
			}
			
			// Get daily search count from external API
			$dailySearchPost = [
				"from" => $check_date,
				"to" => $check_date,
				"id" => $user_id,
				"offset" => 0,
				"searchItem" => ""
			];
			$dailySearchParam = json_encode($dailySearchPost);

			$dailySearchCurl = curl_init();
			curl_setopt_array($dailySearchCurl, array(
				CURLOPT_URL => "https://eitem.in/vehicle/history-whatsap.php",
				CURLOPT_RETURNTRANSFER => true,
				CURLOPT_ENCODING => '',
				CURLOPT_MAXREDIRS => 10,
				CURLOPT_TIMEOUT => 0,
				CURLOPT_FOLLOWLOCATION => true,
				CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				CURLOPT_CUSTOMREQUEST => 'POST',
				CURLOPT_SSL_VERIFYPEER => false,
				CURLOPT_SSL_VERIFYHOST => false,
				CURLOPT_POSTFIELDS => $dailySearchParam,
				CURLOPT_HTTPHEADER => array(
					'Content-Type: application/json',
					'Authorization: ma7641e1064c36f27fa1fd802ea436av5b0a553ead9f97f39de75f2f51b25a983236nish'
				),
			));
			$dailySearchResponse = curl_exec($dailySearchCurl);
			curl_close($dailySearchCurl);

			$dailySearchData = json_decode($dailySearchResponse);
			$truecallerDataCount = (isset($dailySearchData->status) && $dailySearchData->status != '') ? $dailySearchData->totalRows : 0;

			if($truecallerDataCount >=$userlimit){
				return $this->set_response($this->exitDanger('Daily limit over.Please try tomorrow.'), REST_Controller::HTTP_OK);die;
			}

			$post = [
				"mobile"=> $aadhaar,
				"id"=>$user_id,
				"showres"=>1
			];
			
			$param = json_encode($post);
			$curl = curl_init();
			$url = "https://eitem.in/vehicle/fetch-whatsap.php";					

			curl_setopt_array($curl, array(
				CURLOPT_URL => $url,
				CURLOPT_RETURNTRANSFER => true,
				CURLOPT_ENCODING => '',
				CURLOPT_MAXREDIRS => 10,
				CURLOPT_TIMEOUT => 0,
				CURLOPT_FOLLOWLOCATION => true,
				CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				CURLOPT_CUSTOMREQUEST => 'POST',
				CURLOPT_SSL_VERIFYPEER => false,
				CURLOPT_SSL_VERIFYHOST => false,
				CURLOPT_POSTFIELDS =>$param,
				CURLOPT_HTTPHEADER => array(
					'Content-Type: application/json',
					'Authorization: ma7641e1064c36f27fa1fd802ea436av5b0a553ead9f97f39de75f2f51b25a983236nish'
				),
			));

			$response = curl_exec($curl);
			log_message('error', 'whatsapfind$response'.$response);
			$responses = json_decode($response);
			if(isset($responses->status) && $responses->status == true){
				return $this->set_response($this->exitSuccessWithMultiple($responses->response), REST_Controller::HTTP_OK);
			}else{
				return $this->set_response($this->exitDanger($responses->response), REST_Controller::HTTP_OK);
			}
			
			//$responseCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
			
			curl_close($curl);
			return $responses;
			
			/*if(isset($responses->response)){
				$dataInsert = $responses->response;
			}else{
				$dataInsert = '';
			}
			if(isset($responses->status)){
				$status = $responses->status;
				$url = $responses->url;
				$name = $responses->name;
				$details = $responses->details;
				$statusDetails = $responses->statusDetails;
			}else{
				$status = 'FAIL';
				$statusDetails = isset($responses->statusData) ? $responses->statusData : 'N/A';
				$details = isset($responses->details) ? $responses->details : 'N/A';
				$name = isset($responses->name) ? $responses->name : 'N/A';
				$url = isset($responses->url) ? $responses->url : 'N/A';
				$aadhaar = isset($responses->mobile) ? $responses->mobile : 'N/A';
			}
			$inserData = [];
			$inserData = [
				'userId' => $user_id,
				'response' => $dataInsert,
				'status' => $status,
				'mobile' => $aadhaar,
				'url' => $url,
				'name' => $name,
				'details' => $details,
				'statusDetails' => $statusDetails,
				'created_at' => $curr_date
			];

			$this->whatsapModel->attach($inserData);
			if(isset($responses->name) && $responses->status){
				// Array to hold extracted data
				$statusDetails = isset($statusDetails) ? $statusDetails : 'N/A';
				$details = isset($details) ? $details : 'N/A';
				$name = isset($name) ? $name : 'N/A';
				$url = isset($url) ? $url : 'N/A';
				

						// Define regex patterns for each field
						$patterns = [
							'Name' => $name,
							'Details' => $details,
							'Status' => $statusDetails,
							'Mobile' => $aadhaar,
							'Profile Image' => $url
						];
				$result = "<p></p><h3>Mobile Information</h3><p>";						
				$result = $this->getDetails($patterns,$responses->name,$result);
				$result .= "</p>";


				$data = [ "pi" =>$result];
				return $this->set_response($this->exitSuccessWithMultiple($data), REST_Controller::HTTP_OK);
			}else{
				if(isset($responses->response)){
					return $this->set_response($this->exitDanger($responses->response), REST_Controller::HTTP_OK);
				}else{
					return $this->set_response($this->exitDanger("Some issue occured. Please contact admin"), REST_Controller::HTTP_OK);
				}	
			}*/			
        }
    }

}
