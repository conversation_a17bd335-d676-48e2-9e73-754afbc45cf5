<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Token
{

    private $ci;

    function __construct()
    {
        $this->ci = &get_instance();
    }

    function create($payload)
    {
		$values='';
		foreach($payload as $value){
			$values .= $value;
		}
		return encrypt_url($values);
    }
	
	function validateToken(){
		$headers = $this->ci->input->request_headers();
        if (isset($headers['Authorization']) && !empty($headers['Authorization'])) {
            // Extract token from headers
            $client_token = str_replace('Bearer ', '', $headers['Authorization']);
			$this->ci->load->model('userModel');
			$returnData = $this->ci->userModel->findOne(['client_token' => $client_token]);
			if (!isObject($returnData)) {
				return 0;
			}else{
				return 1;
			}
		}else{
			return 0;
		}
	}
	
	function userlimit($user_id, $service = 'vehicle')
	{
		$this->ci->load->model('userModel');
		$this->ci->load->model('settingModel');

		$users = $this->ci->userModel->findOne(['id' => $user_id]);
		if (!$users) {
			return ["status" => false, "error" => "User not exist!"];
		}

		// check expiry
		if (is_null($users->expiryDate) || $users->expiryDate >= date("Y-m-d H:i:s")) {
			$userAccountType = $users->isAccountType; // demo / paid

			// build field name dynamically based on service + account type
			$field = strtolower($service) . '_' . strtolower($userAccountType) . '_daily_credit';

			// fetch from tb_setting (type = ip_grabber)
			$setting = $this->ci->settingModel->findOne([
				'name' => $field,
				'type' => 'ip_grabber'
			]);

			if ($setting) {
				return ["status" => true, "limit" => (int)$setting->value];
			} else {
				return ["status" => false, "error" => "Credit setting not found for {$service} ({$userAccountType})."];
			}

		} else {
			return ["status" => false, "error" => "User not exist or expired. Please contact admin!"];
		}
	}

	function getDailyCredit($user_id, $service = 'voter')
	{
		$this->ci->load->model('userModel');
		$this->ci->load->model('settingModel');

		$users = $this->ci->userModel->findOne(['id' => $user_id]);
		if (!$users) {
			return 0;
		}

		// check expiry
		if (is_null($users->expiryDate) || $users->expiryDate >= date("Y-m-d H:i:s")) {
			$userAccountType = $users->isAccountType; // demo / paid

			// build field name dynamically based on service + account type
			$field = strtolower($service) . '_' . strtolower($userAccountType) . '_daily_credit';

			// fetch from tb_setting (type = ip_grabber)
			$setting = $this->ci->settingModel->findOne([
				'name' => $field,
				'type' => 'ip_grabber'
			]);

			if ($setting) {
				return (int)$setting->value;
			}
		}

		return 0;
	}
	
}
?>